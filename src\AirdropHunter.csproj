﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{88C231E8-FC03-41AB-8A82-E4DCB4E1986B}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <RootNamespace>AirdropHunter</RootNamespace>
    <AssemblyName>AirdropHunter</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BouncyCastle.Crypto, Version=1.8.9.0, Culture=neutral, PublicKeyToken=0e99375e54769942">
      <HintPath>packages\BouncyCastle.1.8.9\lib\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=7.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Logging.Abstractions.7.0.0\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="NBitcoin, Version=7.0.6.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\NBitcoin.7.0.6\lib\net472\NBitcoin.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.ABI, Version=********, Culture=neutral, PublicKeyToken=8768a594786aba4e, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.ABI.4.14.0\lib\net461\Nethereum.ABI.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.Accounts, Version=********, Culture=neutral, PublicKeyToken=8768a594786aba4e, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.Accounts.4.14.0\lib\net461\Nethereum.Accounts.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.BlockchainProcessing, Version=********, Culture=neutral, PublicKeyToken=8768a594786aba4e, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.BlockchainProcessing.4.14.0\lib\net461\Nethereum.BlockchainProcessing.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.Contracts, Version=********, Culture=neutral, PublicKeyToken=8768a594786aba4e, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.Contracts.4.14.0\lib\net461\Nethereum.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.HdWallet, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.HdWallet.4.14.0\lib\net461\Nethereum.HdWallet.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.Hex, Version=********, Culture=neutral, PublicKeyToken=8768a594786aba4e, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.Hex.4.14.0\lib\net461\Nethereum.Hex.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.JsonRpc.Client, Version=********, Culture=neutral, PublicKeyToken=8768a594786aba4e, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.JsonRpc.Client.4.14.0\lib\net461\Nethereum.JsonRpc.Client.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.JsonRpc.RpcClient, Version=********, Culture=neutral, PublicKeyToken=8768a594786aba4e, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.JsonRpc.RpcClient.4.14.0\lib\net461\Nethereum.JsonRpc.RpcClient.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.KeyStore, Version=********, Culture=neutral, PublicKeyToken=8768a594786aba4e, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.KeyStore.4.14.0\lib\net461\Nethereum.KeyStore.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.Merkle.Patricia, Version=********, Culture=neutral, PublicKeyToken=8768a594786aba4e, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.Merkle.Patricia.4.14.0\lib\net461\Nethereum.Merkle.Patricia.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.Model, Version=********, Culture=neutral, PublicKeyToken=8768a594786aba4e, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.Model.4.14.0\lib\net461\Nethereum.Model.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.RLP, Version=********, Culture=neutral, PublicKeyToken=8768a594786aba4e, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.RLP.4.14.0\lib\net461\Nethereum.RLP.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.RPC, Version=********, Culture=neutral, PublicKeyToken=8768a594786aba4e, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.RPC.4.14.0\lib\net461\Nethereum.RPC.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.Signer, Version=********, Culture=neutral, PublicKeyToken=8768a594786aba4e, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.Signer.4.14.0\lib\net461\Nethereum.Signer.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.Signer.EIP712, Version=********, Culture=neutral, PublicKeyToken=8768a594786aba4e, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.Signer.EIP712.4.14.0\lib\net461\Nethereum.Signer.EIP712.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.Util, Version=********, Culture=neutral, PublicKeyToken=8768a594786aba4e, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.Util.4.14.0\lib\net461\Nethereum.Util.dll</HintPath>
    </Reference>
    <Reference Include="Nethereum.Web3, Version=********, Culture=neutral, PublicKeyToken=8768a594786aba4e, processorArchitecture=MSIL">
      <HintPath>packages\Nethereum.Web3.4.14.0\lib\net461\Nethereum.Web3.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Linq" />
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection" />
    <Reference Include="System.Runtime" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions" />
    <Reference Include="System.Runtime.InteropServices" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <EmbeddedResource Include="config.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>