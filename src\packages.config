﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="BouncyCastle" version="1.8.9" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="7.0.0" targetFramework="net472" />
  <package id="NBitcoin" version="7.0.6" targetFramework="net472" />
  <package id="Nethereum.ABI" version="4.14.0" targetFramework="net472" />
  <package id="Nethereum.Accounts" version="4.14.0" targetFramework="net472" />
  <package id="Nethereum.BlockchainProcessing" version="4.14.0" targetFramework="net472" />
  <package id="Nethereum.Contracts" version="4.14.0" targetFramework="net472" />
  <package id="Nethereum.HdWallet" version="4.14.0" targetFramework="net472" />
  <package id="Nethereum.Hex" version="4.14.0" targetFramework="net472" />
  <package id="Nethereum.JsonRpc.Client" version="4.14.0" targetFramework="net472" />
  <package id="Nethereum.JsonRpc.RpcClient" version="4.14.0" targetFramework="net472" />
  <package id="Nethereum.KeyStore" version="4.14.0" targetFramework="net472" />
  <package id="Nethereum.Merkle.Patricia" version="4.14.0" targetFramework="net472" />
  <package id="Nethereum.Model" version="4.14.0" targetFramework="net472" />
  <package id="Nethereum.RLP" version="4.14.0" targetFramework="net472" />
  <package id="Nethereum.RPC" version="4.14.0" targetFramework="net472" />
  <package id="Nethereum.Signer" version="4.14.0" targetFramework="net472" />
  <package id="Nethereum.Signer.EIP712" version="4.14.0" targetFramework="net472" />
  <package id="Nethereum.Util" version="4.14.0" targetFramework="net472" />
  <package id="Nethereum.Web3" version="4.14.0" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
</packages>