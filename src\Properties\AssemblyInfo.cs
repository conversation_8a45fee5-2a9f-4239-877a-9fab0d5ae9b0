﻿using System.Reflection;
using System.Runtime.InteropServices;

[assembly: Assembly<PERSON><PERSON><PERSON>("AirdropHunter")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("AirdropHunter")]
[assembly: AssemblyCopyright("Copyright ©  2023")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]
[assembly: ComVisible(false)]

[assembly: Guid("88c231e8-fc03-41ab-8a82-e4dcb4e1986b")]

[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
